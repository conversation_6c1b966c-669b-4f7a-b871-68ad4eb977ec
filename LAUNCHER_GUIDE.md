# Ironman Ground Items Plugin - Launcher Guide

This guide explains the different ways to run the Ironman Ground Items plugin with your Jagex account.

## Quick Start

### Option 1: Use with Jagex Launcher (Recommended)
1. Run `launch-with-jagex.bat`
2. This will build, deploy, and launch RuneLite with your plugin
3. Login with your Jagex account as normal
4. Enable the plugin in the Plugin Hub

### Option 2: Manual Deployment
1. Run `start.bat` and choose option 2
2. Follow the instructions to launch RuneLite with `--developer-mode`
3. Login with your Jagex account
4. Enable the plugin in the Plugin Hub

## Detailed Instructions

### Method 1: Automated Jagex Launcher Integration

**File: `launch-with-jagex.bat`**

This script will:
- Build your plugin automatically
- Deploy it to RuneLite's sideloaded-plugins directory
- Launch RuneLite with developer mode enabled
- Allow you to use your Jagex account normally

**Steps:**
1. Double-click `launch-with-jagex.bat`
2. Wait for the plugin to build and deploy
3. RuneLite will launch in developer mode
4. Login with your Jagex account as usual
5. Go to Plugin Hub → Installed Plugins
6. Find "Ironman Ground Items" and enable it

### Method 2: Manual Deployment with deploy.bat

**File: `deploy.bat`**

This script copies your built plugin to RuneLite's sideloaded-plugins directory.

**Steps:**
1. Build the plugin: `gradlew shadowJar`
2. Run `deploy.bat`
3. Launch RuneLite manually with developer mode:
   - Option A: Modify your RuneLite shortcut to add `--developer-mode`
   - Option B: Open Command Prompt and run: `RuneLite.exe --developer-mode`
4. Login with your Jagex account
5. Enable the plugin in Plugin Hub

### Method 3: Standalone Development Mode

**File: `start.bat` (Option 1)**

This runs a completely standalone version of RuneLite with your plugin built-in.

**Steps:**
1. Run `start.bat` and choose option 1
2. This launches a development version of RuneLite
3. You can still login with your Jagex account
4. The plugin will be automatically loaded

## Important Notes

### Jagex Account Compatibility
- All methods support Jagex account login
- Your saved credentials from the Jagex Launcher will work
- No need to re-enter your login information

### Developer Mode
- Required for sideloaded plugins to work
- Safe to use - it just enables external plugin loading
- Does not affect your account security
- Your account data remains secure

### Plugin Location
When deployed, your plugin will be located at:
```
%LOCALAPPDATA%\RuneLite\sideloaded-plugins\ironman-ground-items-1.0-SNAPSHOT.jar
```

### Troubleshooting

**Plugin not appearing in Plugin Hub:**
1. Make sure RuneLite is running with `--developer-mode`
2. Check that the plugin JAR is in the sideloaded-plugins directory
3. Restart RuneLite
4. Look in "Installed Plugins" section, not "Available Plugins"

**RuneLite not found:**
1. The scripts will search common installation locations
2. If not found, you'll be prompted to enter the path manually
3. Common locations:
   - `%LOCALAPPDATA%\RuneLite\`
   - `%PROGRAMFILES%\RuneLite\`
   - `%USERPROFILE%\RuneLite\`

**Build failures:**
1. Make sure you have Java 11+ installed
2. Run `gradlew clean` then try building again
3. Check that all files are present in the project directory

### Command Line Options

You can also run these commands manually:

```batch
# Build the plugin
gradlew shadowJar

# Deploy to RuneLite
deploy.bat

# Launch RuneLite with developer mode
RuneLite.exe --developer-mode
```

## Security Notes

- Developer mode only enables external plugin loading
- Your Jagex account credentials remain secure
- The plugin only affects ground item menu entries
- No sensitive data is transmitted or stored

## Plugin Configuration

Once the plugin is loaded:
1. Go to RuneLite Settings
2. Find "Ironman Ground Items" in the plugin list
3. Configure which types of items to hide:
   - Hide Other Player Items (recommended for Ironman)
   - Hide Public Items
   - Hide Own Items
   - Hide Group Items (for Group Ironman)

## Support

If you encounter issues:
1. Check that RuneLite is running in developer mode
2. Verify the plugin JAR exists in sideloaded-plugins directory
3. Try restarting RuneLite
4. Check the RuneLite logs for error messages
