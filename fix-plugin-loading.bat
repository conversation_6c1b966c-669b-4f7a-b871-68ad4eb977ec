@echo off
echo ========================================
echo  Fix Plugin Loading Issues
echo ========================================
echo.

echo This script will try several fixes for plugin loading issues.
echo.

REM Step 1: Clean build
echo Step 1: Clean build...
call .\gradlew.bat clean
call .\gradlew.bat shadowJar

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

REM Step 2: Clear old plugins
echo.
echo Step 2: Clearing old plugin versions...
set "SIDELOAD_DIR=%LOCALAPPDATA%\RuneLite\sideloaded-plugins"

if exist "%SIDELOAD_DIR%" (
    echo Removing old plugin files...
    del "%SIDELOAD_DIR%\ironman-ground-items*.jar" 2>nul
    del "%SIDELOAD_DIR%\example*.jar" 2>nul
) else (
    echo Creating sideloaded-plugins directory...
    mkdir "%SIDELOAD_DIR%"
)

REM Step 3: Copy with simpler name
echo.
echo Step 3: Deploying plugin with simpler name...
copy "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" "%SIDELOAD_DIR%\ironman-ground-items.jar"

if %ERRORLEVEL% neq 0 (
    echo Failed to copy plugin!
    pause
    exit /b 1
)

echo Plugin deployed as: %SIDELOAD_DIR%\ironman-ground-items.jar

REM Step 4: Verify JAR contents
echo.
echo Step 4: Verifying JAR contents...
jar -tf "%SIDELOAD_DIR%\ironman-ground-items.jar" | findstr "IronmanGroundItemsPlugin.class" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ Plugin class found in JAR
) else (
    echo ❌ Plugin class NOT found in JAR - this is the problem!
    echo.
    echo Checking what's actually in the JAR:
    jar -tf "%SIDELOAD_DIR%\ironman-ground-items.jar" | findstr "\.class$" | head -10
    echo.
    pause
    exit /b 1
)

REM Step 5: Check for plugin descriptor
echo.
echo Step 5: Checking plugin descriptor...
jar -tf "%SIDELOAD_DIR%\ironman-ground-items.jar" | findstr "PluginDescriptor" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ Plugin descriptor annotations found
) else (
    echo ⚠️  Plugin descriptor not found in class list
)

REM Step 6: Test with RuneLite
echo.
echo ========================================
echo  Testing with RuneLite
echo ========================================
echo.
echo The plugin has been deployed. Now testing...
echo.
echo 1. Launching RuneLite in developer mode...

if exist "%LOCALAPPDATA%\RuneLite\RuneLite.exe" (
    echo Starting RuneLite...
    start "RuneLite Test" "%LOCALAPPDATA%\RuneLite\RuneLite.exe" --developer-mode --debug
    
    echo.
    echo RuneLite should now be starting with developer mode and debug logging.
    echo.
    echo WHAT TO CHECK:
    echo 1. Look for "Installed Plugins" tab in Plugin Hub
    echo 2. Check if "Ironman Ground Items" appears in the list
    echo 3. Watch the console for any error messages
    echo 4. If plugin appears, try enabling it
    echo.
    echo If the plugin still doesn't appear, there may be a compatibility issue.
    echo Check the RuneLite console output for error messages.
    
) else (
    echo RuneLite.exe not found at: %LOCALAPPDATA%\RuneLite\RuneLite.exe
    echo.
    echo Please manually run:
    echo RuneLite.exe --developer-mode --debug
    echo.
    echo Then check Plugin Hub ^> Installed Plugins for "Ironman Ground Items"
)

echo.
echo ========================================
echo  Additional Troubleshooting
echo ========================================
echo.
echo If the plugin still doesn't work:
echo.
echo 1. Check RuneLite version compatibility
echo    - This plugin was built for latest RuneLite
echo    - Older versions may not support some APIs
echo.
echo 2. Check Java version
echo    - RuneLite requires Java 11+
echo    - Plugin was compiled with Java 11
echo.
echo 3. Check for conflicting plugins
echo    - Disable other ground item related plugins
echo    - Try with minimal plugin setup
echo.
echo 4. Check RuneLite logs
echo    - Look in: %LOCALAPPDATA%\RuneLite\logs\client.log
echo    - Search for "IronmanGroundItems" or "sideloaded"
echo.
echo 5. Try alternative plugin names
echo    - Some special characters might cause issues
echo    - Try renaming to: grounditems.jar
echo.

echo.
pause
