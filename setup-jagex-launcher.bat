@echo off
echo ========================================
echo  Jagex Launcher Developer Mode Setup
echo ========================================
echo.
echo This script will configure your Jagex Launcher to run RuneLite
echo with developer mode enabled, allowing custom plugins to work.
echo.
echo WHAT THIS DOES:
echo 1. Builds and deploys your Ironman Ground Items plugin
echo 2. Creates a modified Jagex Launcher shortcut with developer mode
echo 3. Allows you to use the "Play" button with your custom plugins
echo.
echo Press any key to continue...
pause >nul

REM Step 1: Build and deploy plugin
echo.
echo Step 1: Building and deploying plugin...
echo ========================================
if not exist "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" (
    echo Building plugin...
    call .\gradlew.bat shadowJar
    if %ERRORLEVEL% neq 0 (
        echo Build failed!
        pause
        exit /b 1
    )
)

echo Deploying plugin to RuneLite...
call deploy.bat

REM Step 2: Find Jagex Launcher
echo.
echo Step 2: Locating Jagex Launcher...
echo ========================================

set "JAGEX_LAUNCHER="
if exist "%LOCALAPPDATA%\Programs\Jagex Launcher\JagexLauncher.exe" (
    set "JAGEX_LAUNCHER=%LOCALAPPDATA%\Programs\Jagex Launcher\JagexLauncher.exe"
    echo Found at: %JAGEX_LAUNCHER%
    goto found_jagex
)

if exist "%PROGRAMFILES%\Jagex Launcher\JagexLauncher.exe" (
    set "JAGEX_LAUNCHER=%PROGRAMFILES%\Jagex Launcher\JagexLauncher.exe"
    echo Found at: %JAGEX_LAUNCHER%
    goto found_jagex
)

if exist "%PROGRAMFILES(X86)%\Jagex Launcher\JagexLauncher.exe" (
    set "JAGEX_LAUNCHER=%PROGRAMFILES(X86)%\Jagex Launcher\JagexLauncher.exe"
    echo Found at: %JAGEX_LAUNCHER%
    goto found_jagex
)

echo Jagex Launcher not found in common locations.
echo.
echo Common locations:
echo - %LOCALAPPDATA%\Programs\Jagex Launcher\
echo - %PROGRAMFILES%\Jagex Launcher\
echo - %PROGRAMFILES(X86)%\Jagex Launcher\
echo.
echo Please enter the full path to JagexLauncher.exe:
set /p "JAGEX_LAUNCHER=Path: "

if not exist "%JAGEX_LAUNCHER%" (
    echo Error: Jagex Launcher not found at specified path!
    pause
    exit /b 1
)

:found_jagex
REM Step 3: Create developer mode shortcut
echo.
echo Step 3: Creating developer mode shortcut...
echo ========================================

echo Creating "Jagex Launcher (Developer Mode)" shortcut on desktop...

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Jagex Launcher (Developer Mode).lnk'); $Shortcut.TargetPath = '%JAGEX_LAUNCHER%'; $Shortcut.Arguments = '--runelite-args=\"--developer-mode\"'; $Shortcut.Description = 'Jagex Launcher with RuneLite Developer Mode for Custom Plugins'; $Shortcut.IconLocation = '%JAGEX_LAUNCHER%'; $Shortcut.Save()}"

if %ERRORLEVEL% equ 0 (
    echo Shortcut created successfully!
    goto success
) else (
    echo Shortcut creation failed. Trying alternative method...
    
    REM Alternative method using VBScript
    echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
    echo sLinkFile = "%USERPROFILE%\Desktop\Jagex Launcher (Developer Mode).lnk" >> "%TEMP%\CreateShortcut.vbs"
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.TargetPath = "%JAGEX_LAUNCHER%" >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.Arguments = "--runelite-args=""--developer-mode""" >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.Description = "Jagex Launcher with RuneLite Developer Mode" >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.IconLocation = "%JAGEX_LAUNCHER%" >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
    
    cscript //nologo "%TEMP%\CreateShortcut.vbs"
    del "%TEMP%\CreateShortcut.vbs"
    
    if exist "%USERPROFILE%\Desktop\Jagex Launcher (Developer Mode).lnk" (
        echo Shortcut created successfully using alternative method!
        goto success
    ) else (
        goto manual_setup
    )
)

:success
echo.
echo ========================================
echo  🎉 SETUP COMPLETE! 🎉
echo ========================================
echo.
echo Your Jagex Launcher is now configured for developer mode!
echo.
echo WHAT'S BEEN DONE:
echo ✅ Plugin built and deployed to RuneLite
echo ✅ Developer mode shortcut created on desktop
echo ✅ Ready to use with Jagex account authentication
echo.
echo HOW TO USE:
echo 1. Use the NEW shortcut: "Jagex Launcher (Developer Mode)"
echo 2. Click "Play" to launch RuneLite (same as before)
echo 3. Login with your Jagex account normally
echo 4. Go to Plugin Hub (puzzle piece icon)
echo 5. Click "Installed Plugins" tab
echo 6. Find "Ironman Ground Items" and enable it
echo 7. Configure the plugin settings as needed
echo.
echo IMPORTANT: Always use the new shortcut to ensure developer mode is enabled!
echo.
goto end

:manual_setup
echo.
echo ========================================
echo  Manual Setup Required
echo ========================================
echo.
echo Automatic shortcut creation failed. Please set up manually:
echo.
echo 1. Right-click your existing Jagex Launcher shortcut
echo 2. Select "Properties"
echo 3. In the "Target" field, add this to the end:
echo    --runelite-args="--developer-mode"
echo.
echo Example:
echo Before: "%JAGEX_LAUNCHER%"
echo After:  "%JAGEX_LAUNCHER%" --runelite-args="--developer-mode"
echo.
echo 4. Click "OK" to save
echo 5. Use this modified shortcut to launch with developer mode
echo.

:end
echo Your Ironman Ground Items plugin is ready to use!
echo.
pause
