# Jagex Account Integration for Plugin Development

This guide shows you how to use your Jagex Account with RuneLite development while testing the Ironman Ground Items plugin.

## Prerequisites

- RuneLite launcher version 2.6.3 or newer
- Jagex Account (converted from regular OSRS account)
- Java 11+ installed

## Step-by-Step Setup

### Step 1: Configure RuneLite Launcher

1. **Run RuneLite in configure mode:**
   - **Windows**: Run "RuneLite (configure)" from the Start Menu
   - **Mac**: `/Applications/RuneLite.app/Contents/MacOS/RuneLite --configure`
   - **Linux**: `runelite --configure`

2. **Add the insecure credentials flag:**
   - In the "Client arguments" input box, add: `--insecure-write-credentials`
   - Click "Save"

### Step 2: Generate Credentials

1. **Launch RuneLite via Jagex Launcher:**
   - Use your normal Jagex Launcher
   - Click "Play" to launch RuneLite
   - Login normally with your Jagex Account

2. **Verify credentials were saved:**
   - <PERSON><PERSON><PERSON><PERSON> will write credentials to: `.runelite/credentials.properties`
   - **Windows**: `%USERPROFILE%\.runelite\credentials.properties`
   - **Mac/Linux**: `~/.runelite/credentials.properties`

### Step 3: Test Direct RuneLite Launch

1. **Close RuneLite launched from Jagex Launcher**

2. **Launch RuneLite directly:**
   ```batch
   # Windows
   RuneLite.exe

   # Mac
   /Applications/RuneLite.app/Contents/MacOS/RuneLite

   # Linux
   runelite
   ```

3. **Verify automatic login:**
   - RuneLite should automatically log you in using the saved credentials
   - No password prompt should appear

### Step 4: Development with Plugin

Now you can develop with your plugin using the saved credentials:

1. **Build and run your plugin:**
   ```batch
   start.bat
   ```

2. **This will:**
   - Build the Ironman Ground Items plugin
   - Launch RuneLite with your plugin loaded
   - Automatically login using saved Jagex Account credentials
   - Enable developer mode for debugging

3. **Test your plugin:**
   - Go to Plugin Hub → Installed Plugins
   - Find "Ironman Ground Items" and enable it
   - Configure the plugin settings
   - Test in-game with ground items

## Development Workflow

### Daily Development Process

```batch
# 1. Build and test plugin
start.bat

# 2. Make code changes
# Edit src/main/java/com/example/IronmanGroundItemsPlugin.java

# 3. Rebuild and test
start.bat

# 4. Debug if needed
java -ea -jar build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar --developer-mode --debug
```

### Debugging

For debugging with console output:
```batch
java -ea -jar build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar --developer-mode --debug
```

This will show:
- Plugin loading messages
- Error messages
- Debug output from your plugin

## Security Notes

### Important Security Information

- **Credentials file contains sensitive data** - treat it like a password
- **Do not share** the `credentials.properties` file with anyone
- **Delete when done** - remove the file when you finish development

### Cleanup After Development

1. **Delete credentials file:**
   ```batch
   # Windows
   del "%USERPROFILE%\.runelite\credentials.properties"

   # Mac/Linux
   rm ~/.runelite/credentials.properties
   ```

2. **Invalidate sessions (optional):**
   - Go to runescape.com → Account Settings
   - Click "End sessions" to invalidate all saved credentials

3. **Remove launcher configuration:**
   - Run RuneLite in configure mode again
   - Remove `--insecure-write-credentials` from client arguments
   - Click "Save"

## Troubleshooting

### Credentials Not Working

1. **Check file exists:**
   - Verify `credentials.properties` exists in `.runelite` folder
   - File should contain authentication tokens

2. **Regenerate credentials:**
   - Delete existing `credentials.properties`
   - Launch via Jagex Launcher again
   - New credentials will be generated

### Plugin Not Loading

1. **Check developer mode:**
   - Make sure you see "Installed Plugins" tab in Plugin Hub
   - If not, developer mode isn't enabled

2. **Check plugin file:**
   - Verify `ironman-ground-items-1.0-SNAPSHOT-all.jar` was built
   - Check for build errors in console

3. **Debug mode:**
   ```batch
   java -ea -jar build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar --developer-mode --debug
   ```

### Login Issues

1. **Credentials expired:**
   - Delete `credentials.properties`
   - Regenerate via Jagex Launcher

2. **Account security:**
   - Check if account has been locked
   - Verify 2FA settings haven't changed

## Benefits of This Approach

- ✅ **Secure**: Uses official RuneLite credential system
- ✅ **Convenient**: No need to enter password repeatedly
- ✅ **Development-friendly**: Direct RuneLite launch with debugging
- ✅ **Jagex Account compatible**: Works with converted accounts
- ✅ **Reversible**: Easy to clean up when done

This method gives you the best development experience while maintaining security and compatibility with Jagex Accounts!
