@echo off
echo ========================================
echo  Create RuneLite Developer Mode Shortcut
echo ========================================
echo.

REM Find RuneLite executable
set "RUNELITE_EXE="

echo Searching for RuneLite installation...

if exist "%LOCALAPPDATA%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%LOCALAPPDATA%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%PROGRAMFILES%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%PROGRAMFILES%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%PROGRAMFILES(X86)%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%PROGRAMFILES(X86)%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%USERPROFILE%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%USERPROFILE%\RuneLite\RuneLite.exe"
    goto found_exe
)

echo.
echo RuneLite executable not found in common locations.
echo Please enter the full path to RuneLite.exe:
set /p "RUNELITE_EXE=RuneLite.exe path: "

if not exist "%RUNELITE_EXE%" (
    echo Error: RuneLite executable not found at specified path!
    pause
    exit /b 1
)

:found_exe
echo Found RuneLite at: %RUNELITE_EXE%
echo.

REM Create shortcut using PowerShell
echo Creating desktop shortcut...

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\RuneLite Developer Mode.lnk'); $Shortcut.TargetPath = '%RUNELITE_EXE%'; $Shortcut.Arguments = '--developer-mode'; $Shortcut.Description = 'RuneLite with Developer Mode for Sideloaded Plugins'; $Shortcut.IconLocation = '%RUNELITE_EXE%'; $Shortcut.Save()}"

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo  Shortcut created successfully!
    echo ========================================
    echo.
    echo A new shortcut "RuneLite Developer Mode" has been created on your desktop.
    echo.
    echo This shortcut will launch RuneLite with developer mode enabled,
    echo allowing you to use sideloaded plugins like Ironman Ground Items.
    echo.
    echo You can now:
    echo 1. Use this shortcut to launch RuneLite
    echo 2. Login with your Jagex account as normal
    echo 3. Access your sideloaded plugins in the Plugin Hub
    echo.
) else (
    echo.
    echo Error creating shortcut. You can manually create one by:
    echo 1. Right-clicking on your desktop
    echo 2. New ^> Shortcut
    echo 3. Target: "%RUNELITE_EXE%" --developer-mode
    echo 4. Name: RuneLite Developer Mode
    echo.
)

pause
