@echo off
echo ========================================
echo  Launch RuneLite with Jagex Account
echo ========================================
echo.

REM Check if the plugin JAR exists
if not exist "build\libs\ironman-ground-items-1.0-SNAPSHOT.jar" (
    echo Building plugin first...
    call .\gradlew.bat shadowJar
    if %ERRORLEVEL% neq 0 (
        echo Build failed!
        pause
        exit /b 1
    )
)

REM Find RuneLite executable
echo Searching for RuneLite installation...

set "RUNELITE_EXE="

REM Check common locations for RuneLite executable
if exist "%LOCALAPPDATA%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%LOCALAPPDATA%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%PROGRAMFILES%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%PROGRAMFILES%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%PROGRAMFILES(X86)%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%PROGRAMFILES(X86)%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%USERPROFILE%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%USERPROFILE%\RuneLite\RuneLite.exe"
    goto found_exe
)

REM Check for JAR file instead
if exist "%LOCALAPPDATA%\RuneLite\RuneLite.jar" (
    set "RUNELITE_EXE=java -jar %LOCALAPPDATA%\RuneLite\RuneLite.jar"
    goto found_exe
)

echo.
echo RuneLite executable not found in common locations.
echo Please enter the full path to RuneLite.exe:
set /p "RUNELITE_EXE=RuneLite.exe path: "

:found_exe
REM Deploy plugin first
echo.
echo Deploying plugin...
call deploy.bat

if %ERRORLEVEL% neq 0 (
    echo Plugin deployment failed!
    pause
    exit /b 1
)

echo.
echo Starting RuneLite with developer mode and your Jagex account...
echo.
echo NOTE: This will launch RuneLite in developer mode, which allows
echo sideloaded plugins to work. Your Jagex account login should work normally.
echo.

REM Launch RuneLite with developer mode
if "%RUNELITE_EXE:~0,4%"=="java" (
    %RUNELITE_EXE% --developer-mode
) else (
    "%RUNELITE_EXE%" --developer-mode
)

echo.
echo RuneLite has been launched with developer mode enabled.
echo Your Ironman Ground Items plugin should be available in the Plugin Hub.
echo.
pause
