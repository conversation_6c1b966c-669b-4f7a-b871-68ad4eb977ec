@echo off
echo ========================================
echo  Launch RuneLite with Jagex Account
echo ========================================
echo.

REM Check if the plugin JAR exists
if not exist "build\libs\ironman-ground-items-1.0-SNAPSHOT.jar" (
    echo Building plugin first...
    call .\gradlew.bat shadowJar
    if %ERRORLEVEL% neq 0 (
        echo Build failed!
        pause
        exit /b 1
    )
)

REM Find RuneLite executable
echo Searching for RuneLite installation...

set "RUNELITE_EXE="

REM Check common locations for RuneLite executable
if exist "%LOCALAPPDATA%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%LOCALAPPDATA%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%PROGRAMFILES%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%PROGRAMFILES%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%PROGRAMFILES(X86)%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%PROGRAMFILES(X86)%\RuneLite\RuneLite.exe"
    goto found_exe
)

if exist "%USERPROFILE%\RuneLite\RuneLite.exe" (
    set "RUNELITE_EXE=%USERPROFILE%\RuneLite\RuneLite.exe"
    goto found_exe
)

REM Check for JAR file instead
if exist "%LOCALAPPDATA%\RuneLite\RuneLite.jar" (
    set "RUNELITE_EXE=java -jar %LOCALAPPDATA%\RuneLite\RuneLite.jar"
    goto found_exe
)

echo.
echo RuneLite executable not found in common locations.
echo Please enter the full path to RuneLite.exe:
set /p "RUNELITE_EXE=RuneLite.exe path: "

:found_exe
REM Deploy plugin first
echo.
echo Deploying plugin...
call deploy.bat

if %ERRORLEVEL% neq 0 (
    echo Plugin deployment failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Launching RuneLite with Developer Mode
echo ========================================
echo.
echo This will launch RuneLite in developer mode, which allows
echo sideloaded plugins to work. Your Jagex account login should work normally.
echo.
echo IMPORTANT: After RuneLite starts:
echo 1. Login with your Jagex account as usual
echo 2. Go to Plugin Hub (puzzle piece icon)
echo 3. Click on "Installed Plugins" tab
echo 4. Find "Ironman Ground Items" and enable it
echo 5. Configure the plugin settings as needed
echo.
echo Press any key to launch RuneLite...
pause >nul

REM Launch RuneLite with developer mode
echo Launching RuneLite...
if "%RUNELITE_EXE:~0,4%"=="java" (
    start "RuneLite Developer Mode" %RUNELITE_EXE% --developer-mode
) else (
    start "RuneLite Developer Mode" "%RUNELITE_EXE%" --developer-mode
)

echo.
echo RuneLite has been launched with developer mode enabled!
echo.
echo If RuneLite doesn't start, try one of these alternatives:
echo.
echo 1. Create a shortcut to RuneLite.exe and add --developer-mode to the target
echo 2. Open Command Prompt and run: RuneLite.exe --developer-mode
echo 3. Use the Jagex Launcher and manually enable developer mode
echo.
echo Your Ironman Ground Items plugin should now be available in the Plugin Hub
echo under the "Installed Plugins" section.
echo.
pause
