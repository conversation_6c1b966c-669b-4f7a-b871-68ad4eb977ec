# Ironman Ground Items

A RuneLite plugin that hides non-takeable items from right-click menus for Ironman accounts.

## Features

- **Hide Other Player Items**: Automatically hides items dropped by other players from right-click menus (recommended for Ironman accounts)
- **Configurable Filtering**: Choose which types of items to hide based on ownership
- **Ironman Friendly**: Designed specifically to help Ironman players avoid accidentally clicking on items they cannot take
- **Performance Optimized**: Lightweight implementation that doesn't impact game performance

## Configuration Options

- **Enable Plugin**: Toggle the plugin on/off
- **Hide Other Player Items**: Hide items dropped by other players (recommended for Ironman)
- **Hide Public Items**: Hide items that are public (no specific owner)
- **Hide Own Items**: Hide items that you dropped (not recommended)
- **Hide Group Items**: Hide items dropped by group members (for Group Ironman)

## How It Works

The plugin uses the RuneLite API to detect item ownership through the `TileItem` interface. When you right-click on ground items, the plugin checks the ownership status and removes "Take" options from the menu for items that match your filter settings.

## Installation

### For Jagex Accounts (Recommended)

If you use a Jagex Account and need the launcher for authentication:

1. **Follow the Jagex Account setup guide**: See `JAGEX_ACCOUNT_SETUP.md`
2. **Build and run**: Use `start.bat` to build and launch with your plugin
3. **Configure**: Enable the plugin in Plugin Hub → Installed Plugins

### For Direct RuneLite Login

If you can login directly to RuneLite:

1. **Build the plugin**: `gradlew shadowJar`
2. **Run with plugin**: `start.bat`
3. **Configure**: Enable the plugin in RuneLite settings

## For Ironman Players

This plugin is particularly useful for Ironman accounts as it prevents accidentally clicking on items dropped by other players, which would result in the "You're an ironman, so you can't take this item" message.

## Technical Details

The plugin subscribes to `MenuEntryAdded` events and filters ground item menu entries based on the `TileItem.getOwnership()` API. It supports all ownership types:
- `OWNERSHIP_NONE` (0): Public items
- `OWNERSHIP_SELF` (1): Your own items
- `OWNERSHIP_OTHER` (2): Other players' items
- `OWNERSHIP_GROUP` (3): Group members' items