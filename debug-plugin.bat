@echo off
echo ========================================
echo  Debug Plugin Loading Issues
echo ========================================
echo.

REM Check if shadow JAR exists
echo 1. Checking if plugin JAR exists...
if exist "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" (
    echo ✅ Plugin JAR found: build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar
    
    REM Get file size
    for %%A in ("build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar") do (
        echo    File size: %%~zA bytes
    )
) else (
    echo ❌ Plugin JAR NOT found: build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar
    echo    Building plugin now...
    call .\gradlew.bat shadowJar
    if %ERRORLEVEL% neq 0 (
        echo    Build failed!
        pause
        exit /b 1
    )
)

echo.

REM Find RuneLite directory
echo 2. Checking RuneLite installation...
set "RUNELITE_DIR="
if exist "%LOCALAPPDATA%\RuneLite" (
    set "RUNELITE_DIR=%LOCALAPPDATA%\RuneLite"
    echo ✅ RuneLite found: %RUNELITE_DIR%
) else (
    echo ❌ RuneLite directory not found at: %LOCALAPPDATA%\RuneLite
    echo    Please check your RuneLite installation.
    pause
    exit /b 1
)

echo.

REM Check sideloaded-plugins directory
echo 3. Checking sideloaded-plugins directory...
set "SIDELOAD_DIR=%RUNELITE_DIR%\sideloaded-plugins"
if exist "%SIDELOAD_DIR%" (
    echo ✅ Sideloaded-plugins directory exists: %SIDELOAD_DIR%
    
    echo.
    echo    Contents of sideloaded-plugins directory:
    dir "%SIDELOAD_DIR%" /b
    echo.
    
    if exist "%SIDELOAD_DIR%\ironman-ground-items-1.0-SNAPSHOT-all.jar" (
        echo ✅ Plugin found in sideloaded-plugins directory
        
        REM Get file size in sideloaded directory
        for %%A in ("%SIDELOAD_DIR%\ironman-ground-items-1.0-SNAPSHOT-all.jar") do (
            echo    File size: %%~zA bytes
        )
    ) else (
        echo ❌ Plugin NOT found in sideloaded-plugins directory
        echo    Deploying plugin now...
        call deploy.bat
    )
) else (
    echo ❌ Sideloaded-plugins directory does not exist
    echo    Creating directory and deploying plugin...
    mkdir "%SIDELOAD_DIR%"
    call deploy.bat
)

echo.

REM Check plugin structure
echo 4. Checking plugin JAR structure...
echo.
echo Extracting plugin information...

REM Use Java to check the JAR contents
java -jar "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" --help >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ JAR file is executable
) else (
    echo ⚠️  JAR file may not be properly structured
)

REM Check for plugin descriptor
jar -tf "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" | findstr "IronmanGroundItemsPlugin.class" >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ Plugin class found in JAR
) else (
    echo ❌ Plugin class NOT found in JAR
)

echo.

REM Check RuneLite logs
echo 5. Checking RuneLite logs...
set "LOG_DIR=%RUNELITE_DIR%\logs"
if exist "%LOG_DIR%" (
    echo ✅ Log directory found: %LOG_DIR%
    echo.
    echo Recent log files:
    dir "%LOG_DIR%\*.log" /b /o-d | head -5
    echo.
    echo To check for plugin loading errors, look in the latest client.log file:
    echo %LOG_DIR%\client.log
) else (
    echo ⚠️  Log directory not found
)

echo.

REM Provide troubleshooting steps
echo ========================================
echo  Troubleshooting Steps
echo ========================================
echo.
echo If your plugin still doesn't appear:
echo.
echo 1. Make sure RuneLite is completely closed
echo 2. Delete any old plugin versions from sideloaded-plugins
echo 3. Run: deploy.bat
echo 4. Launch RuneLite with: --developer-mode
echo 5. Check Plugin Hub ^> Installed Plugins (not Available Plugins)
echo.
echo 6. If still not working, check the logs:
echo    %LOG_DIR%\client.log
echo    Look for errors mentioning "IronmanGroundItems" or "sideloaded"
echo.
echo 7. Try renaming the plugin JAR to something simpler:
echo    ironman-ground-items.jar
echo.
echo 8. Verify developer mode is enabled by checking if other
echo    external plugins appear in the Installed Plugins list
echo.

REM Test developer mode
echo ========================================
echo  Testing Developer Mode
echo ========================================
echo.
echo To test if developer mode is working:
echo 1. Launch RuneLite with --developer-mode
echo 2. Go to Plugin Hub
echo 3. Look for "Installed Plugins" tab
echo 4. You should see any .jar files from sideloaded-plugins directory
echo.
echo If you don't see an "Installed Plugins" tab, developer mode is not enabled.
echo.

pause
