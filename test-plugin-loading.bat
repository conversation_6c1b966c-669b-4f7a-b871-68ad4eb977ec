@echo off
echo ========================================
echo  Test Plugin Loading
echo ========================================
echo.

REM Build the plugin first
echo Building plugin...
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

REM Deploy the plugin
echo.
echo Deploying plugin...
call deploy.bat

echo.
echo ========================================
echo  Manual Testing Steps
echo ========================================
echo.
echo Please follow these steps to test if the plugin loads:
echo.
echo 1. Close RuneLite completely if it's running
echo.
echo 2. Open Command Prompt and run:
echo    cd "%LOCALAPPDATA%\RuneLite"
echo    RuneLite.exe --developer-mode
echo.
echo 3. When RuneLite starts, look for these indicators:
echo    - Plugin Hub should have an "Installed Plugins" tab
echo    - Check if "Ironman Ground Items" appears in that tab
echo.
echo 4. If the plugin doesn't appear, check the console output for errors
echo.
echo 5. Alternative test - Launch with verbose logging:
echo    RuneLite.exe --developer-mode --debug
echo.
echo Press any key to open the sideloaded-plugins directory...
pause >nul

REM Open the sideloaded-plugins directory
set "SIDELOAD_DIR=%LOCALAPPDATA%\RuneLite\sideloaded-plugins"
if exist "%SIDELOAD_DIR%" (
    explorer "%SIDELOAD_DIR%"
    echo.
    echo The sideloaded-plugins directory is now open.
    echo Verify that ironman-ground-items-1.0-SNAPSHOT-all.jar is present.
) else (
    echo Error: Sideloaded-plugins directory not found!
)

echo.
echo ========================================
echo  Common Issues and Solutions
echo ========================================
echo.
echo ISSUE: Plugin doesn't appear in Installed Plugins
echo SOLUTIONS:
echo - Make sure RuneLite is launched with --developer-mode
echo - Check that the JAR file is in the sideloaded-plugins directory
echo - Try restarting RuneLite
echo - Check RuneLite logs for errors
echo.
echo ISSUE: "Installed Plugins" tab doesn't exist
echo SOLUTION:
echo - Developer mode is not enabled
echo - Make sure you're using --developer-mode flag
echo.
echo ISSUE: Plugin appears but won't enable
echo SOLUTIONS:
echo - Check RuneLite logs for dependency errors
echo - Make sure the plugin is compatible with your RuneLite version
echo - Try rebuilding the plugin: gradlew clean shadowJar
echo.
echo ISSUE: Plugin enables but doesn't work
echo SOLUTIONS:
echo - Check plugin configuration in RuneLite settings
echo - Look for error messages in RuneLite console
echo - Test on ground items in-game
echo.

echo.
echo Would you like to launch RuneLite in developer mode now? (y/n)
set /p launch="Enter choice: "

if /i "%launch%"=="y" (
    echo.
    echo Launching RuneLite in developer mode...
    echo Look for "Ironman Ground Items" in Plugin Hub ^> Installed Plugins
    echo.
    
    REM Try to find and launch RuneLite
    if exist "%LOCALAPPDATA%\RuneLite\RuneLite.exe" (
        start "RuneLite Developer Mode" "%LOCALAPPDATA%\RuneLite\RuneLite.exe" --developer-mode
    ) else (
        echo RuneLite.exe not found at expected location.
        echo Please manually run: RuneLite.exe --developer-mode
    )
) else (
    echo.
    echo To test manually, run:
    echo RuneLite.exe --developer-mode
    echo.
    echo Then check Plugin Hub ^> Installed Plugins for "Ironman Ground Items"
)

echo.
pause
