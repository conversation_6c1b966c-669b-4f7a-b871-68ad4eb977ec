@echo off
echo ========================================
echo  Deploy Ironman Ground Items Plugin
echo ========================================
echo.

REM Check if the shadow JAR file exists (this is what we need for sideloading)
if not exist "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" (
    echo Error: Plugin shadow JAR file not found!
    echo Please run the build first using: gradlew shadowJar
    echo.
    echo Building now...
    call .\gradlew.bat shadowJar
    if %ERRORLEVEL% neq 0 (
        echo Build failed!
        pause
        exit /b 1
    )
)

REM Find RuneLite installation directory
echo Searching for RuneLite installation...

REM Common RuneLite installation paths
set "RUNELITE_DIR="
set "SIDELOAD_DIR="

REM Check AppData\Local\RuneLite (most common)
if exist "%LOCALAPPDATA%\RuneLite" (
    set "RUNELITE_DIR=%LOCALAPPDATA%\RuneLite"
    echo Found RuneLite at: %RUNELITE_DIR%
    goto found_runelite
)

REM Check Program Files
if exist "%PROGRAMFILES%\RuneLite" (
    set "RUNELITE_DIR=%PROGRAMFILES%\RuneLite"
    echo Found RuneLite at: %RUNELITE_DIR%
    goto found_runelite
)

REM Check Program Files (x86)
if exist "%PROGRAMFILES(X86)%\RuneLite" (
    set "RUNELITE_DIR=%PROGRAMFILES(X86)%\RuneLite"
    echo Found RuneLite at: %RUNELITE_DIR%
    goto found_runelite
)

REM Check user profile
if exist "%USERPROFILE%\RuneLite" (
    set "RUNELITE_DIR=%USERPROFILE%\RuneLite"
    echo Found RuneLite at: %RUNELITE_DIR%
    goto found_runelite
)

REM If not found, ask user for path
echo.
echo RuneLite installation not found in common locations.
echo Please enter the path to your RuneLite installation directory:
echo (Usually something like C:\Users\<USER>\AppData\Local\RuneLite)
echo.
set /p "RUNELITE_DIR=RuneLite path: "

if not exist "%RUNELITE_DIR%" (
    echo Error: The specified RuneLite directory does not exist!
    pause
    exit /b 1
)

:found_runelite
REM Create sideloaded-plugins directory if it doesn't exist
set "SIDELOAD_DIR=%RUNELITE_DIR%\sideloaded-plugins"
if not exist "%SIDELOAD_DIR%" (
    echo Creating sideloaded-plugins directory...
    mkdir "%SIDELOAD_DIR%"
)

REM Copy the shadow JAR to the sideloaded-plugins directory
echo.
echo Copying plugin to RuneLite sideloaded-plugins directory...
echo Source: build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar
echo Target: %SIDELOAD_DIR%\ironman-ground-items-1.0-SNAPSHOT-all.jar

copy "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" "%SIDELOAD_DIR%\"

if %ERRORLEVEL% neq 0 (
    echo.
    echo Error: Failed to copy plugin file!
    pause
    exit /b 1
)

REM Also remove any old versions
if exist "%SIDELOAD_DIR%\ironman-ground-items-1.0-SNAPSHOT.jar" (
    echo Removing old plugin version...
    del "%SIDELOAD_DIR%\ironman-ground-items-1.0-SNAPSHOT.jar"
)

echo.
echo ========================================
echo  Plugin deployed successfully!
echo ========================================
echo.
echo Plugin location: %SIDELOAD_DIR%\ironman-ground-items-1.0-SNAPSHOT-all.jar
echo.
echo IMPORTANT INSTRUCTIONS:
echo.
echo 1. To use this plugin with the Jagex Launcher:
echo    - Close RuneLite if it's currently running
echo    - Launch RuneLite through the Jagex Launcher with the --developer-mode flag
echo.
echo 2. To add the --developer-mode flag:
echo    - Right-click on the RuneLite shortcut or executable
echo    - Add " --developer-mode" to the end of the target path
echo    - Example: "C:\Path\To\RuneLite.exe" --developer-mode
echo.
echo 3. Alternative method (if you can't modify the shortcut):
echo    - Open Command Prompt or PowerShell
echo    - Navigate to your RuneLite installation directory
echo    - Run: RuneLite.exe --developer-mode
echo.
echo 4. Once RuneLite starts in developer mode:
echo    - Go to the Plugin Hub
echo    - Look for "Ironman Ground Items" in the installed plugins
echo    - Enable the plugin and configure it as needed
echo.
echo 5. Your Jagex account credentials should work normally
echo.
echo Note: If the plugin doesn't appear, make sure RuneLite is running
echo in developer mode and restart the client.
echo.
pause
