@echo off
echo ========================================
echo  Check JAR Contents
echo ========================================
echo.

if not exist "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" (
    echo JAR file not found! Building now...
    call .\gradlew.bat shadowJar
)

echo Checking what's inside the JAR file...
echo.

echo === JAR Contents ===
jar -tf "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" | findstr "\.class$"

echo.
echo === Looking for Plugin Class ===
jar -tf "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" | findstr "IronmanGroundItemsPlugin"

echo.
echo === Looking for Config Class ===
jar -tf "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" | findstr "IronmanGroundItemsConfig"

echo.
echo === Checking Package Structure ===
jar -tf "build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar" | findstr "com/example"

echo.
pause
