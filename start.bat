@echo off
echo ========================================
echo  Ironman Ground Items Plugin Launcher
echo ========================================
echo.
echo Choose an option:
echo 1. Build and run with standalone RuneLite (development mode)
echo 2. Deploy plugin for Jagex Launcher (RECOMMENDED)
echo 3. Just build the plugin
echo 4. Setup Jagex Launcher with developer mode
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto standalone
if "%choice%"=="2" goto deploy
if "%choice%"=="3" goto build_only
if "%choice%"=="4" goto setup_jagex
echo Invalid choice. Exiting...
pause
exit /b 1

:build_only
echo.
echo Building Ironman Ground Items Plugin...
echo.
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)
echo.
echo Build successful! JAR file created at: build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar
pause
exit /b 0

:standalone
echo.
echo Building Ironman Ground Items Plugin...
echo.
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful! Starting standalone RuneLite with plugin...
echo.
echo NOTE: This will run RuneLite in development mode with your plugin loaded.
echo You can use your saved account credentials from the Jagex launcher.
echo.

REM Run RuneLite with the plugin (enable assertions)
java -ea -jar build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar

REM Keep the window open if there's an error
if %ERRORLEVEL% neq 0 (
    echo.
    echo RuneLite exited with an error. Check the messages above.
    pause
)
exit /b 0

:deploy
echo.
echo ========================================
echo  Deploy Plugin for Jagex Launcher
echo ========================================
echo.
echo This will build and deploy your plugin to RuneLite, then provide
echo instructions for using it with the Jagex Launcher.
echo.
echo Building plugin for deployment...
echo.
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful! Now deploying to official RuneLite...
call deploy.bat

echo.
echo ========================================
echo  NEXT STEPS FOR JAGEX LAUNCHER:
echo ========================================
echo.
echo 1. Your plugin has been deployed to RuneLite
echo 2. Now you need to enable developer mode in the Jagex Launcher
echo 3. Run option 4 to set this up automatically, or follow these steps:
echo.
echo MANUAL SETUP:
echo a) Find your Jagex Launcher shortcut
echo b) Right-click ^> Properties
echo c) In the Target field, add: --runelite-args="--developer-mode"
echo d) Example: "JagexLauncher.exe" --runelite-args="--developer-mode"
echo.
echo 4. Use the Jagex Launcher "Play" button as normal
echo 5. Your plugin will be available in Plugin Hub ^> Installed Plugins
echo.
pause
exit /b 0

:setup_jagex
echo.
echo ========================================
echo  Setup Jagex Launcher Developer Mode
echo ========================================
echo.
echo This will help you configure the Jagex Launcher to run RuneLite
echo with developer mode enabled, allowing your custom plugins to work.
echo.

REM First, deploy the plugin
echo Step 1: Building and deploying plugin...
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)
call deploy.bat

echo.
echo Step 2: Setting up Jagex Launcher...
echo.

REM Try to find Jagex Launcher
set "JAGEX_LAUNCHER="
if exist "%LOCALAPPDATA%\Programs\Jagex Launcher\JagexLauncher.exe" (
    set "JAGEX_LAUNCHER=%LOCALAPPDATA%\Programs\Jagex Launcher\JagexLauncher.exe"
    goto found_jagex
)

if exist "%PROGRAMFILES%\Jagex Launcher\JagexLauncher.exe" (
    set "JAGEX_LAUNCHER=%PROGRAMFILES%\Jagex Launcher\JagexLauncher.exe"
    goto found_jagex
)

if exist "%PROGRAMFILES(X86)%\Jagex Launcher\JagexLauncher.exe" (
    set "JAGEX_LAUNCHER=%PROGRAMFILES(X86)%\Jagex Launcher\JagexLauncher.exe"
    goto found_jagex
)

echo Jagex Launcher not found in common locations.
echo Please enter the full path to JagexLauncher.exe:
set /p "JAGEX_LAUNCHER=Jagex Launcher path: "

:found_jagex
echo Found Jagex Launcher at: %JAGEX_LAUNCHER%
echo.

REM Create a developer mode shortcut for Jagex Launcher
echo Creating Jagex Launcher Developer Mode shortcut...

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Jagex Launcher (Dev Mode).lnk'); $Shortcut.TargetPath = '%JAGEX_LAUNCHER%'; $Shortcut.Arguments = '--runelite-args=\"--developer-mode\"'; $Shortcut.Description = 'Jagex Launcher with RuneLite Developer Mode'; $Shortcut.IconLocation = '%JAGEX_LAUNCHER%'; $Shortcut.Save()}"

if %ERRORLEVEL% equ 0 (
    echo.
    echo ========================================
    echo  SUCCESS! Setup Complete
    echo ========================================
    echo.
    echo A new shortcut "Jagex Launcher (Dev Mode)" has been created on your desktop.
    echo.
    echo HOW TO USE:
    echo 1. Use the NEW shortcut "Jagex Launcher (Dev Mode)" instead of the regular one
    echo 2. Click "Play" to launch RuneLite as normal
    echo 3. Login with your account through the launcher
    echo 4. Go to Plugin Hub ^> Installed Plugins
    echo 5. Find and enable "Ironman Ground Items"
    echo.
    echo Your plugin is now ready to use with the Jagex Launcher!
    echo.
) else (
    echo.
    echo Shortcut creation failed. Please manually modify your Jagex Launcher shortcut:
    echo 1. Right-click your Jagex Launcher shortcut ^> Properties
    echo 2. In Target field, add: --runelite-args="--developer-mode"
    echo 3. Full example: "%JAGEX_LAUNCHER%" --runelite-args="--developer-mode"
    echo.
)

pause
exit /b 0
