@echo off
echo ========================================
echo  Ironman Ground Items Plugin Launcher
echo ========================================
echo.
echo Choose an option:
echo 1. Build and run with standalone RuneLite (development mode)
echo 2. Build and deploy to official RuneLite for Jagex Launcher
echo 3. Just build the plugin
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto standalone
if "%choice%"=="2" goto deploy
if "%choice%"=="3" goto build_only
echo Invalid choice. Exiting...
pause
exit /b 1

:build_only
echo.
echo Building Ironman Ground Items Plugin...
echo.
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)
echo.
echo Build successful! JAR file created at: build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar
pause
exit /b 0

:standalone
echo.
echo Building Ironman Ground Items Plugin...
echo.
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful! Starting standalone RuneLite with plugin...
echo.
echo NOTE: This will run RuneLite in development mode with your plugin loaded.
echo You can use your saved account credentials from the Jagex launcher.
echo.

REM Run RuneLite with the plugin (enable assertions)
java -ea -jar build\libs\ironman-ground-items-1.0-SNAPSHOT-all.jar

REM Keep the window open if there's an error
if %ERRORLEVEL% neq 0 (
    echo.
    echo RuneLite exited with an error. Check the messages above.
    pause
)
exit /b 0

:deploy
echo.
echo Building plugin for deployment...
echo.
call .\gradlew.bat shadowJar
if %ERRORLEVEL% neq 0 (
    echo.
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful! Now deploying to official RuneLite...
call deploy.bat
exit /b 0
